# Helios AI Korektor Titulků - Seznam úkolů pro implementaci

## Fáze 1: Inicializace a základní struktura

### 1.1 Inicializace projektu
- [x] Vytvořit základní adresářovou strukturu projektu
- [x] Nastavit Git repozitář
- [x] Vytvořit README.md s popisem projektu
- [x] Nastavit .gitignore pro Python, Node.js a Electron

### 1.2 Vytvoření adresářové struktury
```
helios/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py
│   │   ├── config.py
│   │   ├── database.py
│   │   ├── models/
│   │   ├── services/
│   │   ├── api/
│   │   └── utils/
│   ├── tests/
│   ├── requirements.txt
│   └── .env.example
├── frontend/
│   ├── src/
│   │   ├── main.ts
│   │   ├── renderer.ts
│   │   ├── preload.ts
│   │   ├── components/
│   │   ├── services/
│   │   ├── hooks/
│   │   ├── utils/
│   │   └── styles/
│   ├── package.json
│   ├── tsconfig.json
│   └── webpack.config.js
├── shared/
│   └── types/
└── docs/
```

## Fáze 2: Backend implementace (FastAPI + Python)

### 2.1 Základní konfigurace
- [x] Vytvořit requirements.txt s potřebnými závislostmi
- [x] Nastavit config.py pro environment variables
- [x] Vytvořit databázové modely podle PRD
- [x] Nastavit SQLite databázi s SQLAlchemy

### 2.2 Databázové modely
- [x] Vytvořit model Project
- [x] Vytvořit model SubtitleSegment
- [x] Vytvořit model CorrectionSuggestion
- [x] Vytvořit model UserDictionary
- [ ] Vytvořit databázové migrace

### 2.3 API endpoints
- [x] POST /api/projects - vytvoření nového projektu
- [x] GET /api/projects - seznam projektů
- [x] GET /api/projects/{id} - detail projektu
- [x] DELETE /api/projects/{id} - smazání projektu
- [x] POST /api/projects/{id}/process - zpracování videa
- [x] GET /api/projects/{id}/subtitles - získání titulků
- [x] PUT /api/subtitles/{id} - úprava titulků
- [x] POST /api/subtitles/{id}/correct - AI korekce
- [x] GET /api/dictionary - uživatelský slovník
- [x] POST /api/dictionary - přidání slova do slovníku
- [x] DELETE /api/dictionary/{id} - smazání slova

### 2.4 Services
- [x] Vytvořit YouTubeService pro stahování videí
- [x] Vytvořit AudioExtractionService pro extrakci audia
- [x] Vytvořit TranscriptionService s Whisper API
- [x] Vytvořit CorrectionService s GPT-4
- [x] Vytvořit ProjectService pro správu projektů

### 2.5 Validace a bezpečnost
- [x] Implementovat validaci vstupů
- [x] Nastavit CORS pro frontend
- [x] Implementovat rate limiting
- [x] Přidat error handling middleware

## Fáze 3: Frontend implementace (Electron + React + TypeScript)

### 3.1 Základní konfigurace
- [ ] Nastavit package.json pro Electron
- [ ] Nastavit TypeScript konfiguraci
- [ ] Nastavit Webpack pro vývoj
- [ ] Vytvořit základní Electron strukturu

### 3.2 Hlavní komponenty
- [ ] Vytvořit MainWindow komponentu
- [ ] Vytvořit ProjectListPanel komponentu
- [ ] Vytvořit EditorPanel komponentu
- [ ] Vytvořit ActionPanel komponentu
- [ ] Vytvořit DictionaryManager komponentu

### 3.3 Komponenty pro titulky
- [ ] Vytvořit SubtitleList komponentu
- [ ] Vytvořit SubtitleEditor komponentu
- [ ] Vytvořit VideoPreview komponentu
- [ ] Vytvořit Timeline komponentu
- [ ] Vytvořit CorrectionSuggestions komponentu

### 3.4 Services a API klient
- [ ] Vytvořit API service pro komunikaci s backend
- [ ] Vytvořit Project service
- [ ] Vytvořit Subtitle service
- [ ] Vytvořit Dictionary service
- [ ] Vytvořit File service pro lokální operace

### 3.5 State management
- [ ] Nastavit React Context nebo Redux
- [ ] Vytvořit Project store
- [ ] Vytvořit Subtitle store
- [ ] Vytvořit Dictionary store
- [ ] Vytvořit UI state store

### 3.6 Stylování
- [ ] Nastavit CSS framework (Tailwind nebo Styled Components)
- [ ] Vytvořit design systém
- [ ] Implementovat responzivní design
- [ ] Přidat dark/light theme

## Fáze 4: Integrace externích služeb

### 4.1 YouTube integrace
- [x] Nastavit yt-dlp pro stahování videí
- [x] Implementovat URL validaci
- [ ] Přidat progress tracking pro stahování
- [ ] Implementovat error handling pro YouTube API

### 4.2 OpenAI integrace
- [x] Nastavit OpenAI API klienta
- [x] Implementovat Whisper API integraci
- [x] Implementovat GPT-4 integraci pro korekce
- [ ] Přidat retry mechanismus
- [ ] Implementovat rate limiting pro API

### 4.3 Audio processing
- [x] Nastavit FFmpeg pro audio extrakci
- [x] Implementovat audio preprocessing
- [ ] Přidat podporu různých formátů
- [ ] Implementovat audio quality kontrolu

## Fáze 5: Uživatelské rozhraní a UX

### 5.1 Navigace a layout
- [ ] Vytvořit hlavní menu
- [ ] Implementovat sidebar navigaci
- [ ] Vytvořit breadcrumb navigaci
- [ ] Přidat keyboard shortcuts

### 5.2 Formuláře a validace
- [ ] Vytvořit reusable form komponenty
- [ ] Implementovat validaci formulářů
- [ ] Přidat error messages
- [ ] Implementovat loading states

### 5.3 Modal dialogs
- [ ] Vytvořit NewProject modal
- [ ] Vytvořit Settings modal
- [ ] Vytvořit About modal
- [ ] Vytvořit Confirmation dialogs

### 5.4 Notifikace
- [ ] Implementovat toast notifikace
- [ ] Přidat progress bars
- [ ] Implementovat error handling UI
- [ ] Přidat success messages

## Fáze 6: Testování

### 6.1 Backend testy
- [ ] Nastavit pytest framework
- [ ] Vytvořit unit testy pro modely
- [ ] Vytvořit integration testy pro API
- [ ] Vytvořit testy pro services
- [ ] Nastavit test coverage reporting

### 6.2 Frontend testy
- [ ] Nastavit Jest pro React komponenty
- [ ] Vytvořit unit testy pro komponenty
- [ ] Vytvořit integration testy
- [ ] Nastavit E2E testy s Playwright
- [ ] Vytvořit testy pro Electron

### 6.3 Test data
- [ ] Vytvořit test fixtures
- [ ] Přidat sample videos
- [ ] Vytvořit sample titulky
- [ ] Přidat mock API responses

## Fáze 7: Build a deployment

### 7.1 Build konfigurace
- [ ] Nastavit build scripts pro backend
- [ ] Nastavit build scripts pro frontend
- [ ] Konfigurovat Electron build
- [ ] Přidat code signing

### 7.2 Packaging
- [ ] Vytvořit instalátory pro Windows
- [ ] Vytvořit instalátory pro macOS
- [ ] Vytvořit instalátory pro Linux
- [ ] Přidat auto-updater

### 7.3 Dokumentace
- [ ] Vytvořit uživatelskou dokumentaci
- [ ] Vytvořit vývojářskou dokumentaci
- [ ] Přidat API dokumentaci
- [ ] Vytvořit video tutoriály

## Fáze 8: Optimalizace a vylepšení

### 8.1 Performance optimalizace
- [ ] Optimalizovat databázové dotazy
- [ ] Implementovat caching
- [ ] Optimalizovat video processing
- [ ] Přidat lazy loading pro komponenty

### 8.2 Security vylepšení
- [ ] Implementovat API key encryption
- [ ] Přidat input sanitization
- [ ] Implementovat rate limiting
- [ ] Přidat audit logging

### 8.3 Monitoring a analytics
- [ ] Přidat error tracking
- [ ] Implementovat usage analytics
- [ ] Přidat performance monitoring
- [ ] Vytvořit crash reporting

## Fáze 9: Dokončení a release

### 9.1 Final testing
- [ ] Provedení kompletního testování
- [ ] Beta testing s uživateli
- [ ] Bug fixing
- [ ] Performance testing

### 9.2 Release příprava
- [ ] Vytvořit release notes
- [ ] Připravit marketing materiály
- [ ] Nastavit distribuční kanály
- [ ] Přidat feedback mechanismus

### 9.3 Post-release
- [ ] Monitorovat uživatelskou zpětnou vazbu
- [ ] Přidat nové featury podle požadavků
- [ ] Udržovat dokumentaci aktuální
- [ ] Plánovat další verze

## Technické detaily

### Stack
- **Backend**: FastAPI 0.100+, Python 3.12+, SQLAlchemy 2.0+, SQLite
- **Frontend**: Electron 25+, React 18+, TypeScript 5+, Tailwind CSS
- **API**: OpenAI Whisper API, GPT-4-Turbo
- **Tools**: yt-dlp, FFmpeg, Jest, Pytest, Playwright

### Environment variables
- `OPENAI_API_KEY` - OpenAI API klíč
- `DATABASE_URL` - SQLite databáze cesta
- `LOG_LEVEL` - úroveň logování
- `MAX_FILE_SIZE` - maximální velikost souboru

### Build commands
- `npm run dev` - vývojový režim
- `npm run build` - build pro produkci
- `npm run test` - spuštění testů
- `npm run package` - vytvoření instalátorů