/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/main.ts":
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("{\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nconst electron_1 = __webpack_require__(/*! electron */ \"electron\");\nconst path = __importStar(__webpack_require__(/*! path */ \"path\"));\nlet mainWindow = null;\nconst isDev = \"development\" === 'development';\nfunction createWindow() {\n    mainWindow = new electron_1.BrowserWindow({\n        width: 1400,\n        height: 900,\n        minWidth: 1200,\n        minHeight: 700,\n        webPreferences: {\n            nodeIntegration: false,\n            contextIsolation: true,\n            preload: path.join(__dirname, 'preload.js'),\n        },\n        titleBarStyle: 'hiddenInset',\n        show: false,\n    });\n    if (isDev) {\n        mainWindow.loadURL('http://localhost:3000');\n        mainWindow.webContents.openDevTools();\n    }\n    else {\n        mainWindow.loadFile(path.join(__dirname, 'index.html'));\n    }\n    mainWindow.once('ready-to-show', () => {\n        mainWindow?.show();\n    });\n    mainWindow.on('closed', () => {\n        mainWindow = null;\n    });\n}\nelectron_1.app.whenReady().then(() => {\n    createWindow();\n    electron_1.app.on('activate', () => {\n        if (electron_1.BrowserWindow.getAllWindows().length === 0) {\n            createWindow();\n        }\n    });\n});\nelectron_1.app.on('window-all-closed', () => {\n    if (process.platform !== 'darwin') {\n        electron_1.app.quit();\n    }\n});\n// IPC handlers\nelectron_1.ipcMain.handle('app-version', () => {\n    return electron_1.app.getVersion();\n});\n// Menu\nconst template = [\n    {\n        label: 'File',\n        submenu: [\n            {\n                label: 'New Project',\n                accelerator: 'CmdOrCtrl+N',\n                click: () => {\n                    mainWindow?.webContents.send('menu-new-project');\n                },\n            },\n            { type: 'separator' },\n            {\n                label: 'Export SRT',\n                accelerator: 'CmdOrCtrl+E',\n                click: () => {\n                    mainWindow?.webContents.send('menu-export-srt');\n                },\n            },\n            { type: 'separator' },\n            { role: 'quit' },\n        ],\n    },\n    {\n        label: 'Edit',\n        submenu: [\n            { role: 'undo' },\n            { role: 'redo' },\n            { type: 'separator' },\n            { role: 'cut' },\n            { role: 'copy' },\n            { role: 'paste' },\n        ],\n    },\n    {\n        label: 'View',\n        submenu: [\n            { role: 'reload' },\n            { role: 'forceReload' },\n            { role: 'toggleDevTools' },\n            { type: 'separator' },\n            { role: 'resetZoom' },\n            { role: 'zoomIn' },\n            { role: 'zoomOut' },\n            { type: 'separator' },\n            { role: 'togglefullscreen' },\n        ],\n    },\n    {\n        label: 'Window',\n        submenu: [\n            { role: 'minimize' },\n            { role: 'close' },\n        ],\n    },\n];\nconst menu = electron_1.Menu.buildFromTemplate(template);\nelectron_1.Menu.setApplicationMenu(menu);\n\n\n//# sourceURL=webpack://helios-subtitle-corrector/./src/main.ts?\n}");

/***/ }),

/***/ "electron":
/*!***************************!*\
  !*** external "electron" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("electron");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module is referenced by other modules so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__("./src/main.ts");
/******/ 	
/******/ })()
;