import React from 'react';
import ProjectListPanel from './ProjectListPanel';
import EditorPanel from './EditorPanel';
import ActionPanel from './ActionPanel';

const MainLayout: React.FC = () => {
  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Panel - Project List */}
      <div className="w-80 border-r border-gray-200 bg-white">
        <ProjectListPanel />
      </div>

      {/* Center Panel - Editor */}
      <div className="flex-1 flex flex-col">
        <EditorPanel />
      </div>

      {/* Right Panel - Actions & Dictionary */}
      <div className="w-96 border-l border-gray-200 bg-white">
        <ActionPanel />
      </div>
    </div>
  );
};

export default MainLayout;