import React, { useState, useEffect } from 'react';
import { useProjectContext } from '../contexts/ProjectContext';
import { Plus, Loader2, AlertCircle } from 'lucide-react';

const ProjectListPanel: React.FC = () => {
  const { state, loadProjects, loadProject, createProject } = useProjectContext();
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [newProjectUrl, setNewProjectUrl] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  useEffect(() => {
    // Listen for menu events
    if (window.electronAPI) {
      window.electronAPI.onMenuNewProject(() => {
        setShowNewProjectModal(true);
      });

      return () => {
        window.electronAPI?.removeAllListeners('menu-new-project');
      };
    }
  }, []);

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newProjectUrl.trim()) return;

    setIsCreating(true);
    try {
      await createProject(newProjectUrl.trim());
      setNewProjectUrl('');
      setShowNewProjectModal(false);
    } catch (error) {
      console.error('Failed to create project:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing':
        return 'text-blue-600 bg-blue-50';
      case 'needs_review':
        return 'text-orange-600 bg-orange-50';
      case 'completed':
        return 'text-green-600 bg-green-50';
      case 'error':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processing':
        return 'Zpracovává se...';
      case 'needs_review':
        return 'Vyžaduje kontrolu';
      case 'completed':
        return 'Dokončeno';
      case 'error':
        return 'Chyba';
      default:
        return 'Neznámý stav';
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Projekty</h2>
          <button
            onClick={() => setShowNewProjectModal(true)}
            className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            title="Nový projekt"
          >
            <Plus className="w-5 h-5" />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto">
        {state.loading && state.projects.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
          </div>
        ) : state.error ? (
          <div className="p-4 text-center">
            <AlertCircle className="w-8 h-8 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-gray-600">{state.error}</p>
          </div>
        ) : state.projects.length === 0 ? (
          <div className="p-4 text-center">
            <p className="text-sm text-gray-500 mb-4">Žádné projekty</p>
            <button
              onClick={() => setShowNewProjectModal(true)}
              className="text-sm text-blue-600 hover:text-blue-700"
            >
              Vytvořit první projekt
            </button>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {state.projects.map((project) => (
              <div
                key={project.projectId}
                className={`p-4 cursor-pointer hover:bg-gray-50 transition-colors ${
                  state.currentProject?.projectId === project.projectId
                    ? 'bg-blue-50 border-r-2 border-blue-600'
                    : ''
                }`}
                onClick={() => loadProject(project.projectId)}
              >
                <div className="space-y-2">
                  <h3 className="font-medium text-gray-900 truncate">
                    {project.videoTitle || 'Načítá se...'}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
                        project.status
                      )}`}
                    >
                      {getStatusText(project.status)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    {project.createdAt ?
                      new Date(project.createdAt).toLocaleDateString('cs-CZ') :
                      'Neznámé datum'
                    }
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* New Project Modal */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Nový projekt</h3>
            <form onSubmit={handleCreateProject}>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    YouTube URL
                  </label>
                  <input
                    type="url"
                    value={newProjectUrl}
                    onChange={(e) => setNewProjectUrl(e.target.value)}
                    placeholder="https://www.youtube.com/watch?v=..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={isCreating}
                  />
                </div>
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => {
                      setShowNewProjectModal(false);
                      setNewProjectUrl('');
                    }}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                    disabled={isCreating}
                  >
                    Zrušit
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50"
                    disabled={isCreating || !newProjectUrl.trim()}
                  >
                    {isCreating ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin inline mr-2" />
                        Vytváří se...
                      </>
                    ) : (
                      'Vytvořit'
                    )}
                  </button>
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectListPanel;