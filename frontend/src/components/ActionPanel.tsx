import React, { useState } from 'react';
import { useProjectContext } from '../contexts/ProjectContext';
import DictionaryManager from './DictionaryManager';
import { Download, Settings, Info, Loader2 } from 'lucide-react';
import { projectsApi } from '../services/api';

const ActionPanel: React.FC = () => {
  const { state } = useProjectContext();
  const [activeTab, setActiveTab] = useState<'project' | 'dictionary'>('project');
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!state.currentProject) return;

    setIsExporting(true);
    try {
      const blob = await projectsApi.exportProject(state.currentProject.project_id);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${state.currentProject.video_title || 'subtitles'}.srt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export se nezdařil. Zkuste to znovu.');
    } finally {
      setIsExporting(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('cs-CZ', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const canExport = state.currentProject && 
    (state.currentProject.status === 'needs_review' || 
     state.currentProject.status === 'completed');

  return (
    <div className="h-full flex flex-col">
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <div className="flex">
          <button
            onClick={() => setActiveTab('project')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'project'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Projekt
          </button>
          <button
            onClick={() => setActiveTab('dictionary')}
            className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === 'dictionary'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Slovník
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'project' && (
          <div className="p-4 space-y-6">
            {state.currentProject ? (
              <>
                {/* Project Info */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Informace o projektu
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Název videa
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {state.currentProject.video_title || 'Načítá se...'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Délka
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {formatDuration(state.currentProject.video_duration || 0)}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Stav
                      </label>
                      <p className="text-sm mt-1">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                            state.currentProject.status === 'processing'
                              ? 'text-blue-600 bg-blue-50'
                              : state.currentProject.status === 'needs_review'
                              ? 'text-orange-600 bg-orange-50'
                              : state.currentProject.status === 'completed'
                              ? 'text-green-600 bg-green-50'
                              : 'text-red-600 bg-red-50'
                          }`}
                        >
                          {state.currentProject.status === 'processing' && 'Zpracovává se'}
                          {state.currentProject.status === 'needs_review' && 'Vyžaduje kontrolu'}
                          {state.currentProject.status === 'completed' && 'Dokončeno'}
                          {state.currentProject.status === 'error' && 'Chyba'}
                        </span>
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-700">
                        Vytvořeno
                      </label>
                      <p className="text-sm text-gray-900 mt-1">
                        {formatDate(state.currentProject.created_at)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Statistics */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-3">
                    Statistiky
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-gray-900">
                        {state.currentProject.segments?.length || 0}
                      </div>
                      <div className="text-sm text-gray-600">Celkem segmentů</div>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-orange-600">
                        {state.currentProject.segments?.filter(s => s.status === 'NEEDS_REVIEW').length || 0}
                      </div>
                      <div className="text-sm text-gray-600">K revizi</div>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div>
                  <h4 className="text-md font-semibold text-gray-900 mb-3">
                    Akce
                  </h4>
                  <div className="space-y-3">
                    <button
                      onClick={handleExport}
                      disabled={!canExport || isExporting}
                      className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isExporting ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                          Exportuje se...
                        </>
                      ) : (
                        <>
                          <Download className="w-4 h-4 mr-2" />
                          Exportovat do .SRT
                        </>
                      )}
                    </button>
                    
                    {!canExport && (
                      <p className="text-xs text-gray-500 text-center">
                        Export je dostupný až po dokončení zpracování.
                      </p>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <Info className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">Není vybrán žádný projekt.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'dictionary' && <DictionaryManager />}
      </div>
    </div>
  );
};

export default ActionPanel;