import React, { useState, useRef, useEffect } from 'react';
import { SubtitleSegment } from '../types';
import { Check, X, Edit3 } from 'lucide-react';

interface SubtitleSegmentRowProps {
  segment: SubtitleSegment;
  isActive: boolean;
  onSegmentClick: (segmentId: string) => void;
  onTextChange: (segmentId: string, newText: string) => void;
}

const SubtitleSegmentRow: React.FC<SubtitleSegmentRowProps> = ({
  segment,
  isActive,
  onSegmentClick,
  onTextChange,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editText, setEditText] = useState(segment.corrected_text);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (isEditing && textAreaRef.current) {
      textAreaRef.current.focus();
      textAreaRef.current.select();
    }
  }, [isEditing]);

  const formatTime = (timeMs: number): string => {
    const seconds = Math.floor(timeMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    const ms = timeMs % 1000;

    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
  };

  const handleSave = () => {
    onTextChange(segment.segment_id, editText);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditText(segment.corrected_text);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const getStatusClassName = () => {
    switch (segment.status) {
      case 'needs_review':
        return 'border-orange-200 bg-orange-50';
      case 'auto_corrected':
        return 'border-green-200 bg-green-50';
      case 'user_modified':
        return 'border-blue-200 bg-blue-50';
      default:
        return 'border-gray-200';
    }
  };

  const getStatusColor = () => {
    switch (segment.status) {
      case 'needs_review':
        return 'text-orange-600';
      case 'auto_corrected':
        return 'text-green-600';
      case 'user_modified':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const renderTextDiff = () => {
    if (segment.original_text === segment.corrected_text) {
      return <span>{segment.corrected_text}</span>;
    }

    // Simple diff highlighting
    const originalWords = segment.original_text.split(' ');
    const correctedWords = segment.corrected_text.split(' ');

    return (
      <div className="space-y-1">
        <div className="text-sm text-gray-500 line-through">
          {segment.original_text}
        </div>
        <div className="text-gray-900">
          {segment.corrected_text}
        </div>
      </div>
    );
  };

  return (
    <div
      className={`p-4 cursor-pointer transition-all ${
        isActive ? 'bg-blue-50 border-l-4 border-blue-500' : 'hover:bg-gray-50'
      } ${getStatusClassName()}`}
      onClick={() => onSegmentClick(segment.segment_id)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-mono text-gray-600">
              {formatTime(segment.start_time_ms)}
            </span>
            <span className="text-gray-400">→</span>
            <span className="text-sm font-mono text-gray-600">
              {formatTime(segment.end_time_ms)}
            </span>
            <span
              className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()} bg-opacity-10`}
            >
              {segment.status}
            </span>
          </div>

          {isEditing ? (
            <div className="space-y-2">
              <textarea
                ref={textAreaRef}
                value={editText}
                onChange={(e) => setEditText(e.target.value)}
                onKeyDown={handleKeyDown}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows={2}
                onClick={(e) => e.stopPropagation()}
              />
              <div className="flex items-center space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSave();
                  }}
                  className="p-1 text-green-600 hover:bg-green-100 rounded"
                >
                  <Check className="w-4 h-4" />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleCancel();
                  }}
                  className="p-1 text-red-600 hover:bg-red-100 rounded"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <div
                className="text-gray-900 cursor-text"
                onDoubleClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
              >
                {renderTextDiff()}
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
                className="p-1 text-gray-400 hover:text-gray-600"
              >
                <Edit3 className="w-4 h-4" />
              </button>
            </div>
          )}

          {segment.suggestions.length > 0 && (
            <div className="mt-3 space-y-2">
              <div className="text-xs font-medium text-gray-600">
                Návrhy korekcí:
              </div>
              {segment.suggestions.map((suggestion) => (
                <div
                  key={suggestion.suggestion_id}
                  className="text-xs bg-gray-100 p-2 rounded"
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-gray-700">
                      {suggestion.type}
                    </span>
                    <span className="text-gray-500">
                      {Math.round(suggestion.confidence * 100)}%
                    </span>
                  </div>
                  <div className="text-gray-600">
                    {suggestion.description}
                  </div>
                  <div className="mt-1">
                    <span className="line-through text-red-600">
                      "{suggestion.original_fragment}"
                    </span>
                    <span className="mx-2">→</span>
                    <span className="text-green-600">
                      "{suggestion.suggested_fragment}"
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubtitleSegmentRow;