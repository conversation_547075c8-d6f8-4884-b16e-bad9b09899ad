export interface Project {
  projectId: string;
  youtubeUrl: string;
  videoTitle?: string;
  videoDuration: number;
  status: 'processing' | 'needs_review' | 'completed' | 'error';
  createdAt: string;
  updatedAt: string;
  errorMessage?: string;
  segments: SubtitleSegment[];
  statistics: {
    totalSegments: number;
    needsReviewCount: number;
    autoCorrectionsCount: number;
    userModifiedCount: number;
  };
}

export interface ProjectSummary {
  projectId: string;
  youtubeUrl: string;
  videoTitle?: string;
  videoDuration: number;
  status: 'processing' | 'needs_review' | 'completed' | 'error';
  createdAt: string;
  updatedAt: string;
}

export interface SubtitleSegment {
  segmentId: string;
  sequenceNumber: number;
  startTimeMs: number;
  endTimeMs: number;
  originalText: string;
  correctedText: string;
  status: 'unchanged' | 'auto_corrected' | 'needs_review' | 'user_modified';
  suggestions: CorrectionSuggestion[];
}

export interface CorrectionSuggestion {
  suggestionId: string;
  type: 'dictionary_match' | 'punctuation' | 'capitalization' | 'semantic_rewrite';
  confidence: number;
  description: string;
  originalFragment: string;
  suggestedFragment: string;
  applied: boolean;
}

export interface UserDictionaryTerm {
  termId: string;
  phrase: string;
  caseSensitive: boolean;
  createdAt: string;
}

export interface CreateProjectRequest {
  youtube_url: string;
}

export interface UpdateSegmentRequest {
  correctedText: string;
}

export interface CreateDictionaryTermRequest {
  phrase: string;
  caseSensitive: boolean;
}