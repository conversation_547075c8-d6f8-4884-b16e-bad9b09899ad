from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    # API Keys
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    youtube_api_key: Optional[str] = Field(None, env="YOUTUBE_API_KEY")
    secret_key: str = Field("your-secret-key-here-change-in-production", env="SECRET_KEY")
    
    # Database
    database_url: str = Field("sqlite:///./helios.db", env="DATABASE_URL")
    
    # Application
    app_name: str = "Helios Subtitle Corrector"
    app_version: str = "1.0.0"
    debug: bool = Field(False, env="DEBUG")
    log_level: str = Field("INFO", env="LOG_LEVEL")
    
    # CORS
    cors_origins: list = ["http://localhost:3000", "http://localhost:5173"]
    
    # File storage
    UPLOAD_DIR: str = "./uploads"
    TEMP_DIR: str = "./temp"
    
    # Processing limits
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    max_video_duration: int = 3600  # 1 hour in seconds
    
    # OpenAI settings
    OPENAI_MODEL: str = "gpt-4-turbo-preview"
    WHISPER_MODEL: str = "whisper-1"
    
    class Config:
        env_file = ".env"
        case_sensitive = False


settings = Settings()