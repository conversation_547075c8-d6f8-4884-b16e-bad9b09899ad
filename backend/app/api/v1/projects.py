"""Project management endpoints for <PERSON><PERSON>s subtitle corrector."""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from sqlalchemy import desc

from app.core.database import get_db
from app.models.project import Project
from app.schemas.project import ProjectCreate, ProjectResponse, ProjectUpdate
from app.services.processing_service import ProcessingService

router = APIRouter(prefix="/projects", tags=["projects"])


@router.post("/", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Create a new project and start processing."""
    try:
        # Initialize processing service
        processing_service = ProcessingService()

        # Validate YouTube URL
        youtube_url = str(project.youtube_url)
        if not processing_service.youtube_service.validate_url(youtube_url):
            raise HTTPException(status_code=400, detail="Invalid YouTube URL")

        # Extract video info
        try:
            video_info = processing_service.youtube_service.extract_video_info(youtube_url)
            video_id = youtube_url.split('v=')[1].split('&')[0] if 'v=' in youtube_url else "unknown"
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Failed to extract video info: {str(e)}")

        # Create project
        db_project = Project(
            title=video_info.get('title', 'Nový projekt'),
            youtube_url=youtube_url,
            youtube_video_id=video_id,
            # duration=video_info.get('duration', 0),  # temporarily disabled
            status="processing"
        )

        db.add(db_project)
        db.commit()
        db.refresh(db_project)

        # Start processing in background
        background_tasks.add_task(
            processing_service.process_project,
            db_project.id,
            db
        )

        return db_project

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create project: {str(e)}")


@router.get("/", response_model=List[ProjectResponse])
async def get_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    status: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get user's projects with optional filtering."""
    query = db.query(Project).order_by(desc(Project.created_at))

    if status:
        query = query.filter(Project.status == status)

    projects = query.offset(skip).limit(limit).all()
    return projects


@router.get("/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: int,
    db: Session = Depends(get_db)
):
    """Get specific project details."""
    project = db.query(Project).filter(
        Project.id == project_id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return project


@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    db: Session = Depends(get_db)
):
    """Update project details."""
    project = db.query(Project).filter(
        Project.id == project_id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    update_data = project_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(project, field, value)
    
    db.commit()
    db.refresh(project)
    return project


@router.delete("/{project_id}")
async def delete_project(
    project_id: int,
    db: Session = Depends(get_db)
):
    """Delete a project and all associated data."""
    project = db.query(Project).filter(
        Project.id == project_id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    db.delete(project)
    db.commit()
    
    return {"message": "Project deleted successfully"}


@router.post("/{project_id}/reprocess")
async def reprocess_project(
    project_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Reprocess a project with current settings."""
    project = db.query(Project).filter(
        Project.id == project_id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    # Reset project status
    project.status = "processing"
    project.error_message = None
    db.commit()
    
    # Start reprocessing
    processing_service = ProcessingService(db)
    background_tasks.add_task(
        processing_service.process_project,
        project_id
    )
    
    return {"message": "Reprocessing started"}


@router.get("/{project_id}/status")
async def get_project_status(
    project_id: int,
    db: Session = Depends(get_db)
):
    """Get detailed project processing status."""
    project = db.query(Project).filter(
        Project.id == project_id
    ).first()
    
    if not project:
        raise HTTPException(status_code=404, detail="Project not found")
    
    return {
        "id": project.id,
        "name": project.name,
        "status": project.status,
        "progress": project.progress,
        "error_message": project.error_message,
        "created_at": project.created_at,
        "updated_at": project.updated_at,
        "subtitle_count": len(project.subtitle_segments),
        "correction_count": sum(
            len(segment.correction_suggestions)
            for segment in project.subtitle_segments
        )
    }