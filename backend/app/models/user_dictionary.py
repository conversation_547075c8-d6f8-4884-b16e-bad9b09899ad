from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class UserDictionary(Base):
    __tablename__ = "user_dictionary"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    original_word = Column(String(255), nullable=False)
    corrected_word = Column(String(255), nullable=False)
    context = Column(Text)
    frequency = Column(Integer, default=1)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    user = relationship("User", back_populates="dictionary_entries")

    def __repr__(self):
        return f"<UserDictionary(id={self.id}, original='{self.original_word}', corrected='{self.corrected_word}')>"