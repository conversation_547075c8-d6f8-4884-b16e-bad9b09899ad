from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, ForeignKey, Float
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(255), nullable=False)
    youtube_url = Column(String(500), nullable=False)
    youtube_video_id = Column(String(50), nullable=False)
    original_language = Column(String(10), nullable=False, default="auto")
    target_language = Column(String(10), nullable=False, default="cs")
    status = Column(String(50), nullable=False, default="pending")
    # duration = Column(Integer, default=0)  # Video duration in seconds - temporarily disabled
    audio_file_path = Column(String(500))
    original_subtitle_path = Column(String(500))
    corrected_subtitle_path = Column(String(500))
    processing_started_at = Column(DateTime(timezone=True))
    processing_completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    subtitle_segments = relationship("SubtitleSegment", back_populates="project", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Project(id={self.id}, title='{self.title}', status='{self.status}')>"