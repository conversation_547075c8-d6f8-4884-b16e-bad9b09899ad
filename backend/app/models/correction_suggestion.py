from sqlalchemy import Column, Integer, String, DateTime, Text, Float, Foreign<PERSON>ey, <PERSON>olean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class CorrectionSuggestion(Base):
    __tablename__ = "correction_suggestions"

    id = Column(Integer, primary_key=True, index=True)
    subtitle_segment_id = Column(Integer, ForeignKey("subtitle_segments.id"), nullable=False)
    original_text = Column(Text, nullable=False)
    suggested_text = Column(Text, nullable=False)
    confidence_score = Column(Float)
    suggestion_type = Column(String(50), nullable=False)  # 'grammar', 'spelling', 'context', 'punctuation', 'style'
    reason = Column(Text)
    is_accepted = Column(Boolean, default=False)
    is_rejected = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    subtitle_segment = relationship("SubtitleSegment", back_populates="correction_suggestions")

    def __repr__(self):
        return f"<CorrectionSuggestion(id={self.id}, type={self.suggestion_type}, accepted={self.is_accepted})>"