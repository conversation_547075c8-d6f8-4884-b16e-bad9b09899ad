"""Project service for managing projects and processing."""

from sqlalchemy.orm import Session
from app.models import Project as ProjectModel, SubtitleSegment as SegmentModel
from app.schemas import Project, ProjectSummary
from app.services.youtube_service import YouTubeService
from app.services.ai_service import AIService
import uuid
from datetime import datetime
from typing import List, Optional


class ProjectService:
    """Service for managing projects."""
    
    def __init__(self, db: Session):
        self.db = db
        self.youtube_service = YouTubeService()
        self.ai_service = AIService()
    
    def create_project(self, youtube_url: str) -> Project:
        """Vytvoří nový projekt v databázi"""
        project_id = str(uuid.uuid4())
        
        project = ProjectModel(
            project_id=project_id,
            youtube_url=youtube_url,
            status="PENDING",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        self.db.add(project)
        self.db.commit()
        self.db.refresh(project)
        
        return Project.model_validate(project)
    
    def get_all_projects(self) -> List[ProjectSummary]:
        """Vrátí seznam všech projektů"""
        projects = self.db.query(ProjectModel).all()
        return [ProjectSummary.model_validate(p) for p in projects]
    
    def get_project_by_id(self, project_id: str) -> Optional[Project]:
        """Vrátí projekt podle ID včetně segmentů"""
        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()
        
        if not project:
            return None
            
        return Project.model_validate(project)
    
    def delete_project(self, project_id: str) -> bool:
        """Smaže projekt"""
        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()
        
        if not project:
            return False
        
        self.db.delete(project)
        self.db.commit()
        return True
    
    def process_video(self, project_id: str):
        """Zpracuje video na pozadí"""
        try:
            project = self.db.query(ProjectModel).filter(
                ProjectModel.project_id == project_id
            ).first()
            
            if not project:
                return
            
            # Aktualizuj status na PROCESSING
            project.status = "PROCESSING"
            self.db.commit()
            
            # Stáhni video info
            video_info = self.youtube_service.get_video_info(project.youtube_url)
            project.video_title = video_info.get("title")
            project.video_duration = video_info.get("duration")
            
            # Extrahuj titulky
            subtitles = self.youtube_service.extract_subtitles(project.youtube_url)
            
            # Zpracuj AI korekce
            corrected_segments = self.ai_service.process_subtitles(subtitles)
            
            # Ulož segmenty do databáze
            for i, segment in enumerate(corrected_segments):
                segment_model = SegmentModel(
                    segment_id=str(uuid.uuid4()),
                    project_id=project_id,
                    sequence_number=i + 1,
                    start_time_ms=segment["start_time_ms"],
                    end_time_ms=segment["end_time_ms"],
                    original_text=segment["original_text"],
                    corrected_text=segment["corrected_text"],
                    status="NEEDS_REVIEW" if segment["has_corrections"] else "APPROVED"
                )
                self.db.add(segment_model)
            
            project.status = "COMPLETED"
            project.updated_at = datetime.utcnow()
            self.db.commit()
            
        except Exception as e:
            project.status = "ERROR"
            project.error_message = str(e)
            project.updated_at = datetime.utcnow()
            self.db.commit()
    
    def update_segment_text(self, segment_id: str, corrected_text: str):
        """Aktualizuje text segmentu"""
        segment = self.db.query(SegmentModel).filter(
            SegmentModel.segment_id == segment_id
        ).first()
        
        if segment:
            segment.corrected_text = corrected_text
            segment.status = "APPROVED"
            self.db.commit()
            self.db.refresh(segment)
        
        return segment
    
    def apply_correction_suggestion(self, suggestion_id: str) -> bool:
        """Aplikuje návrh korekce"""
        # Implementace aplikace návrhu korekce
        return True
    
    def generate_srt_file(self, project_id: str) -> Optional[str]:
        """Generuje SRT soubor pro projekt"""
        project = self.db.query(ProjectModel).filter(
            ProjectModel.project_id == project_id
        ).first()
        
        if not project:
            return None
        
        segments = self.db.query(SegmentModel).filter(
            SegmentModel.project_id == project_id
        ).order_by(SegmentModel.sequence_number).all()
        
        srt_content = ""
        for segment in segments:
            start_time = self._ms_to_srt_time(segment.start_time_ms)
            end_time = self._ms_to_srt_time(segment.end_time_ms)
            
            srt_content += f"{segment.sequence_number}\n"
            srt_content += f"{start_time} --> {end_time}\n"
            srt_content += f"{segment.corrected_text}\n\n"
        
        return srt_content
    
    def _ms_to_srt_time(self, ms: int) -> str:
        """Převede milisekundy na SRT formát času"""
        hours = ms // 3600000
        minutes = (ms % 3600000) // 60000
        seconds = (ms % 60000) // 1000
        milliseconds = ms % 1000
        
        return f"{hours:02d}:{minutes:02d}:{seconds:02d},{milliseconds:03d}"
