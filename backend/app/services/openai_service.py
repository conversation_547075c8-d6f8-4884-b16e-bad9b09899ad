"""OpenAI service for transcription and correction using Whisper and GPT-4."""

import os
import logging
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import openai
from fastapi import HTTPException

from app.core.config import settings
from app.models.subtitle_segment import SubtitleSegment
from app.models.correction_suggestion import CorrectionSuggestion

logger = logging.getLogger(__name__)


class OpenAIService:
    """Service for OpenAI API interactions including Whisper and GPT-4."""
    
    def __init__(self):
        if settings.openai_api_key:
            self.client = openai.AsyncOpenAI(api_key=settings.openai_api_key)
        else:
            self.client = None
        self.model = settings.OPENAI_MODEL
        self.whisper_model = settings.WHISPER_MODEL
    
    async def transcribe_audio(
        self,
        audio_file_path: str,
        language: Optional[str] = None,
        prompt: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Transcribe audio file using OpenAI Whisper."""
        try:
            if not self.client:
                raise HTTPException(
                    status_code=500,
                    detail="OpenAI API key not configured"
                )

            audio_path = Path(audio_file_path)
            if not audio_path.exists():
                raise HTTPException(
                    status_code=404,
                    detail=f"Audio file not found: {audio_file_path}"
                )
            
            # Check file size (25MB limit for Whisper API)
            file_size = audio_path.stat().st_size
            if file_size > 25 * 1024 * 1024:
                raise HTTPException(
                    status_code=413,
                    detail="Audio file too large. Maximum size is 25MB"
                )
            
            with open(audio_path, 'rb') as audio_file:
                transcription = await self.client.audio.transcriptions.create(
                    model=self.whisper_model,
                    file=audio_file,
                    language=language,
                    prompt=prompt,
                    response_format="verbose_json",
                    timestamp_granularities=["segment"]
                )
            
            # Convert to our format
            segments = []
            for segment in transcription.segments:
                segments.append({
                    'start_time': segment.start,
                    'end_time': segment.end,
                    'text': segment.text.strip(),
                    'confidence': getattr(segment, 'avg_logprob', 0.0)
                })
            
            logger.info(f"Transcribed {len(segments)} segments from audio")
            return segments
            
        except openai.APIError as e:
            logger.error(f"OpenAI API error during transcription: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Transcription failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during transcription: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Transcription failed: {str(e)}"
            )
    
    async def correct_subtitle_text(
        self,
        text: str,
        context: Optional[str] = None,
        language: str = "cs",
        custom_dictionary: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Correct subtitle text using GPT-4."""
        try:
            # Build system prompt
            system_prompt = f"""You are a professional subtitle corrector for {language} language. 
Your task is to correct spelling, grammar, and punctuation errors in subtitle text while preserving the original meaning and timing.

Rules:
1. Only correct actual errors (spelling, grammar, punctuation)
2. Do not change the meaning or add/remove content
3. Keep the same approximate length for timing purposes
4. Preserve speaker style and tone
5. Use proper {language} spelling and grammar
6. Maintain subtitle formatting conventions"""

            if custom_dictionary and custom_dictionary:
                system_prompt += f"\n\nCustom dictionary corrections to apply:\n" + "\n".join(
                    f"- {word}" for word in custom_dictionary
                )

            if context:
                system_prompt += f"\n\nContext: {context}"

            user_prompt = f"""Please correct the following subtitle text:

Original: "{text}"

Return your response in JSON format with:
- "corrected": the corrected text
- "confidence": confidence score (0-1)
- "changes": list of specific changes made
- "explanation": brief explanation of corrections"""

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=500,
                response_format={"type": "json_object"}
            )
            
            result = response.choices[0].message.content
            import json
            correction_data = json.loads(result)
            
            logger.info(f"Corrected subtitle text: {text} -> {correction_data.get('corrected', text)}")
            return correction_data
            
        except openai.APIError as e:
            logger.error(f"OpenAI API error during correction: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Correction failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during correction: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Correction failed: {str(e)}"
            )
    
    async def correct_subtitle_segments(
        self,
        segments: List[Dict[str, Any]],
        language: str = "cs",
        custom_dictionary: Optional[List[str]] = None,
        batch_size: int = 10
    ) -> List[Dict[str, Any]]:
        """Correct multiple subtitle segments in batches."""
        try:
            corrected_segments = []
            
            # Process in batches to avoid rate limits
            for i in range(0, len(segments), batch_size):
                batch = segments[i:i + batch_size]
                batch_corrections = await self._correct_batch(
                    batch, language, custom_dictionary
                )
                corrected_segments.extend(batch_corrections)
                
                # Small delay between batches
                if i + batch_size < len(segments):
                    await asyncio.sleep(1)
            
            logger.info(f"Corrected {len(corrected_segments)} subtitle segments")
            return corrected_segments
            
        except Exception as e:
            logger.error(f"Error correcting subtitle segments: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Correction failed: {str(e)}"
            )
    
    async def _correct_batch(
        self,
        segments: List[Dict[str, Any]],
        language: str,
        custom_dictionary: Optional[List[str]]
    ) -> List[Dict[str, Any]]:
        """Correct a batch of segments."""
        try:
            # Build context from previous segments
            context = " ".join([seg['text'] for seg in segments[-3:]]) if len(segments) > 3 else ""
            
            corrections = []
            for segment in segments:
                correction = await self.correct_subtitle_text(
                    segment['text'],
                    context=context,
                    language=language,
                    custom_dictionary=custom_dictionary
                )
                
                corrections.append({
                    'original_text': segment['text'],
                    'corrected_text': correction.get('corrected', segment['text']),
                    'confidence': correction.get('confidence', 0.0),
                    'changes': correction.get('changes', []),
                    'explanation': correction.get('explanation', ''),
                    'start_time': segment['start_time'],
                    'end_time': segment['end_time']
                })
                
                # Update context
                context = " ".join([context, segment['text']])[-500:]  # Keep last 500 chars
            
            return corrections
            
        except Exception as e:
            logger.error(f"Error in batch correction: {str(e)}")
            raise
    
    async def generate_correction_suggestions(
        self,
        segments: List[SubtitleSegment],
        language: str = "cs",
        custom_dictionary: Optional[List[str]] = None
    ) -> List[CorrectionSuggestion]:
        """Generate correction suggestions for subtitle segments."""
        try:
            segment_dicts = [
                {
                    'id': seg.id,
                    'text': seg.original_text,
                    'start_time': seg.start_time,
                    'end_time': seg.end_time
                }
                for seg in segments
            ]
            
            corrections = await self.correct_subtitle_segments(
                segment_dicts,
                language=language,
                custom_dictionary=custom_dictionary
            )
            
            suggestions = []
            for correction in corrections:
                if correction['corrected_text'] != correction['original_text']:
                    suggestion = CorrectionSuggestion(
                        subtitle_segment_id=correction.get('id'),
                        original_text=correction['original_text'],
                        suggested_text=correction['corrected_text'],
                        confidence=correction['confidence'],
                        changes=correction['changes'],
                        explanation=correction['explanation']
                    )
                    suggestions.append(suggestion)
            
            logger.info(f"Generated {len(suggestions)} correction suggestions")
            return suggestions
            
        except Exception as e:
            logger.error(f"Error generating suggestions: {str(e)}")
            raise
    
    async def translate_subtitle_text(
        self,
        text: str,
        target_language: str = "en",
        source_language: str = "cs"
    ) -> Dict[str, Any]:
        """Translate subtitle text to target language."""
        try:
            system_prompt = f"""You are a professional subtitle translator. 
Translate the given text from {source_language} to {target_language} while:
1. Preserving the meaning and tone
2. Keeping similar length for timing purposes
3. Using natural, conversational language
4. Maintaining subtitle formatting"""

            user_prompt = f"""Translate this subtitle text:

"{text}"

Return JSON with:
- "translated": the translated text
- "confidence": confidence score (0-1)
- "notes": any translation notes"""

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=500,
                response_format={"type": "json_object"}
            )
            
            result = response.choices[0].message.content
            import json
            translation_data = json.loads(result)
            
            logger.info(f"Translated text: {text} -> {translation_data.get('translated', text)}")
            return translation_data
            
        except openai.APIError as e:
            logger.error(f"OpenAI API error during translation: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Translation failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error during translation: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Translation failed: {str(e)}"
            )
    
    async def get_supported_languages(self) -> List[str]:
        """Get list of supported languages for transcription."""
        # Whisper supports these languages
        return [
            "af", "ar", "hy", "az", "be", "bs", "bg", "ca", "zh", "hr", "cs", "da",
            "nl", "en", "et", "fi", "fr", "gl", "de", "el", "he", "hi", "hu", "is",
            "id", "it", "ja", "kn", "kk", "ko", "lv", "lt", "mk", "ms", "mr", "mi",
            "ne", "no", "fa", "pl", "pt", "ro", "ru", "sr", "sk", "sl", "es", "sw",
            "sv", "tl", "ta", "th", "tr", "uk", "ur", "vi", "cy"
        ]
    
    def estimate_cost(
        self,
        audio_duration_seconds: int,
        text_length: int,
        correction_segments: int
    ) -> Dict[str, float]:
        """Estimate OpenAI API costs."""
        # Whisper pricing: $0.006 per minute
        whisper_cost = (audio_duration_seconds / 60) * 0.006
        
        # GPT-4 pricing: $0.03 per 1K tokens (input), $0.06 per 1K tokens (output)
        # Rough estimate: 1 token ≈ 4 characters
        input_tokens = text_length / 4
        output_tokens = input_tokens * 1.2  # 20% overhead for corrections
        
        gpt_cost = (input_tokens / 1000) * 0.03 + (output_tokens / 1000) * 0.06
        
        total_cost = whisper_cost + gpt_cost
        
        return {
            "whisper_cost": round(whisper_cost, 4),
            "gpt_cost": round(gpt_cost, 4),
            "total_cost": round(total_cost, 4),
            "currency": "USD"
        }