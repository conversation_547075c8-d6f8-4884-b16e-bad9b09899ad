"""SRT subtitle file generation service."""

import logging
from datetime import timedelta
from typing import List

from app.models.subtitle_segment import SubtitleSegment

logger = logging.getLogger(__name__)


class SRTGenerator:
    """Service for generating SRT subtitle files."""
    
    def generate_srt(self, segments: List[SubtitleSegment]) -> str:
        """Generate SRT content from subtitle segments."""
        try:
            srt_lines = []
            
            for segment in segments:
                # Sequence number
                srt_lines.append(str(segment.sequence_number))
                
                # Time range
                start_time = self._format_time(segment.start_time)
                end_time = self._format_time(segment.end_time)
                srt_lines.append(f"{start_time} --> {end_time}")
                
                # Text content
                text = segment.corrected_text or segment.original_text
                srt_lines.append(text.strip())
                
                # Empty line between segments
                srt_lines.append("")
            
            # Remove trailing empty line
            if srt_lines and srt_lines[-1] == "":
                srt_lines.pop()
            
            return "\n".join(srt_lines)
            
        except Exception as e:
            logger.error(f"Error generating SRT: {str(e)}")
            raise
    
    def _format_time(self, seconds: float) -> str:
        """Format time in SRT format (HH:MM:SS,mmm)."""
        try:
            # Convert to timedelta for easy formatting
            td = timedelta(seconds=seconds)
            total_seconds = int(td.total_seconds())
            
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            seconds = total_seconds % 60
            milliseconds = int((seconds - int(seconds)) * 1000)
            
            return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"
            
        except Exception as e:
            logger.error(f"Error formatting time: {str(e)}")
            return "00:00:00,000"
    
    def parse_srt(self, srt_content: str) -> List[dict]:
        """Parse SRT content into segment dictionaries."""
        try:
            segments = []
            lines = srt_content.strip().split('\n')
            
            current_segment = {}
            text_lines = []
            
            for line in lines:
                line = line.strip()
                
                if not line:
                    # Empty line - finalize current segment
                    if current_segment and text_lines:
                        current_segment['text'] = ' '.join(text_lines)
                        segments.append(current_segment)
                        current_segment = {}
                        text_lines = []
                    continue
                
                if line.isdigit() and not current_segment:
                    # Sequence number
                    current_segment['sequence_number'] = int(line)
                elif '-->' in line and 'sequence_number' in current_segment:
                    # Time range
                    start_str, end_str = line.split('-->')
                    current_segment['start_time'] = self._parse_srt_time(start_str.strip())
                    current_segment['end_time'] = self._parse_srt_time(end_str.strip())
                elif 'start_time' in current_segment:
                    # Text content
                    text_lines.append(line)
            
            # Handle last segment
            if current_segment and text_lines:
                current_segment['text'] = ' '.join(text_lines)
                segments.append(current_segment)
            
            return segments
            
        except Exception as e:
            logger.error(f"Error parsing SRT: {str(e)}")
            raise
    
    def _parse_srt_time(self, time_str: str) -> float:
        """Parse SRT time format to seconds."""
        try:
            # Format: HH:MM:SS,mmm
            time_str = time_str.replace(',', '.')
            parts = time_str.split(':')
            
            if len(parts) != 3:
                raise ValueError("Invalid time format")
            
            hours = int(parts[0])
            minutes = int(parts[1])
            seconds = float(parts[2])
            
            return hours * 3600 + minutes * 60 + seconds
            
        except Exception as e:
            logger.error(f"Error parsing SRT time: {str(e)}")
            return 0.0
    
    def validate_srt(self, srt_content: str) -> dict:
        """Validate SRT content and return validation report."""
        try:
            segments = self.parse_srt(srt_content)
            
            report = {
                "valid": True,
                "segments_count": len(segments),
                "errors": [],
                "warnings": []
            }
            
            # Check for duplicate sequence numbers
            sequence_numbers = [s['sequence_number'] for s in segments]
            if len(sequence_numbers) != len(set(sequence_numbers)):
                report["errors"].append("Duplicate sequence numbers found")
                report["valid"] = False
            
            # Check for time overlaps
            for i in range(1, len(segments)):
                prev_end = segments[i-1]['end_time']
                curr_start = segments[i]['start_time']
                
                if curr_start < prev_end:
                    report["warnings"].append(
                        f"Time overlap between segments {i} and {i+1}"
                    )
            
            # Check for empty text
            for i, segment in enumerate(segments):
                if not segment['text'].strip():
                    report["warnings"].append(
                        f"Empty text in segment {i+1}"
                    )
            
            # Check for invalid time ranges
            for i, segment in enumerate(segments):
                if segment['start_time'] >= segment['end_time']:
                    report["errors"].append(
                        f"Invalid time range in segment {i+1}"
                    )
                    report["valid"] = False
            
            return report
            
        except Exception as e:
            return {
                "valid": False,
                "segments_count": 0,
                "errors": [str(e)],
                "warnings": []
            }
    
    def merge_segments(self, segments: List[dict], max_duration: float = 5.0) -> List[dict]:
        """Merge short segments to improve readability."""
        try:
            if not segments:
                return []
            
            merged = []
            current = segments[0].copy()
            
            for segment in segments[1:]:
                duration = segment['end_time'] - current['start_time']
                
                if (duration <= max_duration and 
                    len(current['text']) + len(segment['text']) < 80):
                    # Merge segments
                    current['end_time'] = segment['end_time']
                    current['text'] += ' ' + segment['text']
                else:
                    # Add current to merged and start new
                    merged.append(current)
                    current = segment.copy()
            
            # Add last segment
            merged.append(current)
            
            # Update sequence numbers
            for i, segment in enumerate(merged):
                segment['sequence_number'] = i + 1
            
            return merged
            
        except Exception as e:
            logger.error(f"Error merging segments: {str(e)}")
            return segments
    
    def split_long_segments(self, segments: List[dict], max_length: int = 40) -> List[dict]:
        """Split long segments at sentence boundaries."""
        try:
            split_segments = []
            
            for segment in segments:
                text = segment['text']
                
                if len(text) <= max_length:
                    split_segments.append(segment)
                    continue
                
                # Split at sentence boundaries
                sentences = self._split_into_sentences(text)
                
                if len(sentences) == 1:
                    # No sentence boundaries, split by words
                    words = text.split()
                    chunks = [words[i:i+max_length//5] for i in range(0, len(words), max_length//5)]
                    
                    for i, chunk in enumerate(chunks):
                        new_segment = segment.copy()
                        new_segment['text'] = ' '.join(chunk)
                        new_segment['sequence_number'] = len(split_segments) + 1
                        
                        # Adjust timing proportionally
                        if len(chunks) > 1:
                            duration = segment['end_time'] - segment['start_time']
                            chunk_duration = duration / len(chunks)
                            new_segment['start_time'] = segment['start_time'] + (i * chunk_duration)
                            new_segment['end_time'] = segment['start_time'] + ((i + 1) * chunk_duration)
                        
                        split_segments.append(new_segment)
                else:
                    # Split by sentences
                    for i, sentence in enumerate(sentences):
                        new_segment = segment.copy()
                        new_segment['text'] = sentence.strip()
                        new_segment['sequence_number'] = len(split_segments) + 1
                        
                        # Adjust timing proportionally
                        if len(sentences) > 1:
                            duration = segment['end_time'] - segment['start_time']
                            sentence_duration = duration / len(sentences)
                            new_segment['start_time'] = segment['start_time'] + (i * sentence_duration)
                            new_segment['end_time'] = segment['start_time'] + ((i + 1) * sentence_duration)
                        
                        split_segments.append(new_segment)
            
            return split_segments
            
        except Exception as e:
            logger.error(f"Error splitting segments: {str(e)}")
            return segments
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """Split text into sentences."""
        import re
        
        # Simple sentence splitting
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]