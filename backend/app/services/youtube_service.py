"""YouTube service for downloading videos and extracting subtitles."""

import yt_dlp
from typing import Dict, List, Any
import re


class YouTubeService:
    """Service for handling YouTube video operations."""

    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """Validuje YouTube URL"""
        youtube_patterns = [
            r'youtube\.com/watch\?v=',
            r'youtu\.be/',
            r'youtube\.com/embed/',
            r'youtube\.com/v/'
        ]
        return any(re.search(pattern, url) for pattern in youtube_patterns)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """Získá informace o videu"""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                "title": info.get("title"),
                "duration": info.get("duration"),
                "description": info.get("description"),
                "uploader": info.get("uploader")
            }

    def extract_subtitles(self, url: str) -> List[Dict[str, Any]]:
        """Extrahuje titulky z videa"""
        ydl_opts = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['cs', 'en'],
            'skip_download': True,
            'quiet': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            # Zpracuj titulky do strukturovaného formátu
            subtitles = []
            if 'subtitles' in info or 'automatic_captions' in info:
                # Implementace parsování titulků
                pass

            return subtitles
