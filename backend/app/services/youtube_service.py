"""YouTube service for downloading videos and extracting subtitles."""

import yt_dlp
from typing import Dict, List, Any
import re


class YouTubeService:
    """Service for handling YouTube video operations."""

    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """Validuje YouTube URL"""
        youtube_patterns = [
            r'youtube\.com/watch\?v=',
            r'youtu\.be/',
            r'youtube\.com/embed/',
            r'youtube\.com/v/'
        ]
        return any(re.search(pattern, url) for pattern in youtube_patterns)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """Získá informace o videu"""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                "title": info.get("title"),
                "duration": info.get("duration"),
                "description": info.get("description"),
                "uploader": info.get("uploader")
            }

    def extract_subtitles(self, url: str) -> List[Dict[str, Any]]:
        """Extrahuje titulky z videa"""
        import tempfile
        import os
        import re

        with tempfile.TemporaryDirectory() as temp_dir:
            ydl_opts = {
                'writesubtitles': True,
                'writeautomaticsub': True,
                'subtitleslangs': ['cs', 'cs-CZ', 'en'],
                'skip_download': True,
                'quiet': True,
                'outtmpl': os.path.join(temp_dir, '%(title)s.%(ext)s'),
            }

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)

                # Najdi stažené titulky
                subtitle_files = []
                for file in os.listdir(temp_dir):
                    if file.endswith(('.vtt', '.srt')):
                        subtitle_files.append(os.path.join(temp_dir, file))

                if not subtitle_files:
                    return []

                # Parsuj první nalezený soubor titulků
                subtitle_file = subtitle_files[0]
                return self._parse_subtitle_file(subtitle_file)

    def _parse_subtitle_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Parsuje soubor titulků do strukturovaného formátu"""
        import re

        segments = []

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        if file_path.endswith('.vtt'):
            # WebVTT formát
            pattern = r'(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})\n(.*?)(?=\n\n|\n\d{2}:|\Z)'
            matches = re.findall(pattern, content, re.DOTALL)

            for i, (start, end, text) in enumerate(matches):
                start_ms = self._time_to_ms(start)
                end_ms = self._time_to_ms(end)
                clean_text = re.sub(r'<[^>]+>', '', text).strip()

                if clean_text:
                    segments.append({
                        "start_time_ms": start_ms,
                        "end_time_ms": end_ms,
                        "text": clean_text,
                        "sequence": i + 1
                    })

        return segments

    def _time_to_ms(self, time_str: str) -> int:
        """Převede čas ve formátu HH:MM:SS.mmm na milisekundy"""
        parts = time_str.split(':')
        hours = int(parts[0])
        minutes = int(parts[1])
        seconds_parts = parts[2].split('.')
        seconds = int(seconds_parts[0])
        milliseconds = int(seconds_parts[1])

        return (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds

    def download_audio(self, url: str, output_path: str) -> str:
        """Stáhne audio z YouTube videa"""
        ydl_opts = {
            'format': 'bestaudio/best',
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'mp3',
                'preferredquality': '192',
            }],
            'outtmpl': output_path,
            'quiet': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([url])

        # Vrať cestu k audio souboru
        audio_file = output_path.replace('.%(ext)s', '.mp3')
        return audio_file
