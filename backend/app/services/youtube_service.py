"""YouTube service for downloading videos and extracting subtitles."""

import yt_dlp
from typing import Dict, List, Any
import re


class YouTubeService:
    """Service for handling YouTube video operations."""

    @staticmethod
    def is_valid_youtube_url(url: str) -> bool:
        """Validuje YouTube URL"""
        youtube_patterns = [
            r'youtube\.com/watch\?v=',
            r'youtu\.be/',
            r'youtube\.com/embed/',
            r'youtube\.com/v/'
        ]
        return any(re.search(pattern, url) for pattern in youtube_patterns)

    def get_video_info(self, url: str) -> Dict[str, Any]:
        """Získá informace o videu"""
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            return {
                "title": info.get("title"),
                "duration": info.get("duration"),
                "description": info.get("description"),
                "uploader": info.get("uploader")
            }

    def extract_subtitles(self, url: str) -> List[Dict[str, Any]]:
        """Extrahuje titulky z videa"""
        ydl_opts = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': ['cs', 'en'],
            'skip_download': True,
            'quiet': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            # Zpracuj titulky do strukturovaného formátu
            subtitles = []
            if 'subtitles' in info or 'automatic_captions' in info:
                # Implementace parsování titulků
                pass

            return subtitles
    
    def extract_audio(
        self, 
        url: str, 
        output_path: Optional[str] = None,
        audio_format: str = 'wav',
        audio_quality: str = '192'
    ) -> str:
        """Extract audio from YouTube video."""
        try:
            if output_path is None:
                output_path = str(self.temp_dir / "%(title)s.%(ext)s")
            
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': output_path,
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': audio_format,
                    'preferredquality': audio_quality,
                }],
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                
                # Get the actual audio file path
                base_filename = ydl.prepare_filename(info)
                audio_file = Path(base_filename).with_suffix(f'.{audio_format}')
                
                if not audio_file.exists():
                    # Try with different extensions
                    for ext in ['wav', 'mp3', 'm4a', 'flac']:
                        test_file = Path(base_filename).with_suffix(f'.{ext}')
                        if test_file.exists():
                            audio_file = test_file
                            break
                
                logger.info(f"Extracted audio: {audio_file}")
                return str(audio_file)
                
        except Exception as e:
            logger.error(f"Failed to extract audio: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to extract audio: {str(e)}"
            )
    
    def get_subtitles(
        self, 
        url: str, 
        language: str = 'en',
        auto_subtitles: bool = True
    ) -> Optional[str]:
        """Download subtitles from YouTube video."""
        try:
            ydl_opts = {
                'writesubtitles': True,
                'writeautomaticsub': auto_subtitles,
                'subtitleslangs': [language],
                'skip_download': True,
                'outtmpl': str(self.temp_dir / "%(title)s.%(ext)s"),
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                # Check if subtitles are available
                subtitles = info.get('subtitles', {})
                automatic_captions = info.get('automatic_captions', {})
                
                available_subs = {}
                if subtitles:
                    available_subs.update(subtitles)
                if auto_subtitles and automatic_captions:
                    available_subs.update(automatic_captions)
                
                if language in available_subs:
                    # Download the subtitle file
                    subtitle_info = available_subs[language]
                    if isinstance(subtitle_info, list) and subtitle_info:
                        subtitle_url = subtitle_info[0].get('url')
                        if subtitle_url:
                            import requests
                            response = requests.get(subtitle_url)
                            response.raise_for_status()
                            
                            subtitle_file = self.temp_dir / f"{info.get('title', 'subtitles')}_{language}.srt"
                            with open(subtitle_file, 'w', encoding='utf-8') as f:
                                f.write(response.text)
                            
                            logger.info(f"Downloaded subtitles: {subtitle_file}")
                            return str(subtitle_file)
                
                logger.warning(f"No subtitles found for language: {language}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get subtitles: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get subtitles: {str(e)}"
            )
    
    def get_available_languages(self, url: str) -> list:
        """Get list of available subtitle languages."""
        try:
            ydl_opts = {
                'listsubtitles': True,
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False)
                
                subtitles = info.get('subtitles', {})
                automatic_captions = info.get('automatic_captions', {})
                
                languages = set()
                languages.update(subtitles.keys())
                languages.update(automatic_captions.keys())
                
                return sorted(list(languages))
                
        except Exception as e:
            logger.error(f"Failed to get available languages: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get available languages: {str(e)}"
            )
    
    def validate_url(self, url: str) -> bool:
        """Validate if URL is a valid YouTube URL."""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.extract_info(url, download=False, process=False)
                return True
                
        except Exception:
            return False
    
    def get_video_id(self, url: str) -> Optional[str]:
        """Extract video ID from YouTube URL."""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=False, process=False)
                return info.get('id')
                
        except Exception:
            return None
    
    def cleanup_temp_files(self, pattern: str = "*") -> int:
        """Clean up temporary files matching pattern."""
        cleaned_count = 0
        
        try:
            for file_path in self.temp_dir.glob(pattern):
                if file_path.is_file():
                    file_path.unlink()
                    cleaned_count += 1
                    
            logger.info(f"Cleaned up {cleaned_count} temporary files")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup temp files: {str(e)}")
            return 0