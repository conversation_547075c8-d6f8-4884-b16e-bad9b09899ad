"""Pydantic schemas for request/response validation."""

from .user import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserLogin,
    UserToken,
    UserPasswordReset,
    UserPasswordChange,
    UserProfileUpdate,
    UserList,
    UserSearch,
    UserStats
)

from .project import (
    ProjectBase,
    ProjectCreate,
    ProjectUpdate,
    ProjectResponse,
    ProjectList,
    ProjectSearch,
    ProjectStats,
    ProjectExport,
    ProjectImport
)

from .subtitle_segment import (
    SubtitleSegmentBase,
    SubtitleSegmentCreate,
    SubtitleSegmentUpdate,
    SubtitleSegmentResponse,
    SubtitleSegmentList,
    SubtitleSegmentSearch,
    SubtitleSegmentBatchUpdate,
    SubtitleSegmentExport,
    SubtitleSegmentImport
)

from .correction_suggestion import (
    CorrectionSuggestionBase,
    CorrectionSuggestionCreate,
    CorrectionSuggestionUpdate,
    CorrectionSuggestionResponse,
    CorrectionSuggestionList,
    CorrectionSuggestionSearch,
    CorrectionSuggestionBatchUpdate,
    CorrectionSuggestionExport,
    CorrectionSuggestionImport,
    CorrectionStats
)

from .user_dictionary import (
    UserDictionaryBase,
    UserDictionaryCreate,
    UserDictionaryUpdate,
    UserDictionaryResponse,
    UserDictionaryList,
    DictionarySearch,
    DictionaryImport,
    DictionaryExport,
    DictionaryStats
)

__all__ = [
    # User schemas
    "UserBase",
    "UserCreate",
    "UserUpdate",
    "UserResponse",
    "UserLogin",
    "UserToken",
    "UserPasswordReset",
    "UserPasswordChange",
    "UserProfileUpdate",
    "UserList",
    "UserSearch",
    "UserStats",
    
    # Project schemas
    "ProjectBase",
    "ProjectCreate",
    "ProjectUpdate",
    "ProjectResponse",
    "ProjectList",
    "ProjectSearch",
    "ProjectStats",
    "ProjectExport",
    "ProjectImport",
    
    # Subtitle segment schemas
    "SubtitleSegmentBase",
    "SubtitleSegmentCreate",
    "SubtitleSegmentUpdate",
    "SubtitleSegmentResponse",
    "SubtitleSegmentList",
    "SubtitleSegmentSearch",
    "SubtitleSegmentBatchUpdate",
    "SubtitleSegmentExport",
    "SubtitleSegmentImport",
    
    # Correction suggestion schemas
    "CorrectionSuggestionBase",
    "CorrectionSuggestionCreate",
    "CorrectionSuggestionUpdate",
    "CorrectionSuggestionResponse",
    "CorrectionSuggestionList",
    "CorrectionSuggestionSearch",
    "CorrectionSuggestionBatchUpdate",
    "CorrectionSuggestionExport",
    "CorrectionSuggestionImport",
    "CorrectionStats",
    
    # User dictionary schemas
    "UserDictionaryBase",
    "UserDictionaryCreate",
    "UserDictionaryUpdate",
    "UserDictionaryResponse",
    "UserDictionaryList",
    "DictionarySearch",
    "DictionaryImport",
    "DictionaryExport",
    "DictionaryStats",
]