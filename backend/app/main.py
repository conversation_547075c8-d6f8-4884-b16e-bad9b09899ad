"""Main FastAPI application for AI Korektor Titulků backend."""

import os
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware

from app.core.config import settings
from app.core.database import create_tables
from app.api.v1.projects import router as projects_router
from app.api.v1.dictionary import dictionary_router

# Konfigurace loggingu
logging.basicConfig(level=settings.LOG_LEVEL)
logger = logging.getLogger(__name__)


# Vytvoření FastAPI aplikace
app = FastAPI(
    title="AI Korektor Titulků",
    description="API pro automatickou korekci českých titulků pomocí AI",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Registrace routerů
app.include_router(projects_router, prefix="/api/v1")
app.include_router(dictionary_router, prefix="/api/v1")

@app.on_event("startup")
async def startup_event():
    """Inicializace při startu aplikace"""
    logger.info("Spouštím AI Korektor Titulků...")
    create_tables()
    os.makedirs(settings.TEMP_DIR, exist_ok=True)
    logger.info("Databáze inicializována")


@app.get("/")
async def root():
    """Health check endpoint"""
    return {"message": "AI Korektor Titulků API", "status": "running"}

@app.get("/health")
async def health_check():
    """Detailní health check"""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "database": "connected"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
